package org.galiasystems.csms.test;

import org.galiasystems.csms.test.annotations.ResetAfterTest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Example test class demonstrating the usage of @ResetAfterTest annotation.
 * This test shows how to use the annotation to reset the database after test execution.
 *
 * Note: This is a simple unit test that doesn't require Quarkus context to demonstrate
 * the annotation functionality.
 */
public class ResetAfterTestExampleTest {

    @Test
    @DisplayName("Example test that modifies database and resets after")
    @ResetAfterTest
    public void testThatModifiesDatabaseWithReset() {
        // This test would modify the database
        // After this test completes, the database will be reset to initial state

        // Simple assertion to make the test pass
        assertTrue(true, "Test with @ResetAfterTest annotation");

        // In a real test, you would:
        // 1. Modify database data
        // 2. Verify the modifications
        // 3. The @ResetAfterTest annotation ensures database is reset after this test
    }

    @Test
    @DisplayName("Example test without database reset")
    public void testWithoutDatabaseReset() {
        // This test does not have @ResetAfterTest annotation
        // Database will not be reset after this test

        assertTrue(true, "Test without @ResetAfterTest annotation");
    }
}
