# @ResetAfterTest Annotation

The `@ResetAfterTest` annotation provides a convenient way to reset the database to its initial state after a test method completes. This is particularly useful for tests that modify database data and need to ensure a clean state for subsequent tests.

## How it works

When a test method is annotated with `@ResetAfterTest`, the following happens after the test completes:

1. **Database Cleanup**: The entire database is cleaned (all objects are dropped)
2. **Migration Execution**: All Flyway migration scripts from `resources/db/migration/` are executed
3. **Data Import**: The `resources/db/data/import.sql` script is executed to populate initial test data

## Usage

Simply add the `@ResetAfterTest` annotation to any test method:

```java
@Test
@ResetAfterTest
public void testThatModifiesDatabase() {
    // Test code that modifies database
    // Database will be reset after this test completes
}
```

## Example

```java
@QuarkusTest
public class MyDatabaseTest {

    @Inject
    ChargingStationRepository chargingStationRepository;

    @Test
    @DisplayName("Test that creates new charging stations")
    @ResetAfterTest
    public void testCreateChargingStations() {
        // Create some charging stations
        // Modify database data
        
        // After this test completes, database will be reset
    }

    @Test
    @DisplayName("Test that runs with clean database")
    public void testWithCleanDatabase() {
        // This test will run with the clean database state
        // because the previous test reset the database
    }
}
```

## When to use

Use `@ResetAfterTest` when:

- Your test modifies database data that could affect other tests
- You need to ensure a clean database state for subsequent tests
- You're testing database operations that create, update, or delete data

## When NOT to use

Avoid `@ResetAfterTest` when:

- Your test only reads data without modifications
- You're using `@TestReactiveTransaction` (which already provides rollback)
- Performance is critical (database reset has overhead)

## Performance Considerations

Database reset involves:
- Dropping all database objects
- Running all migration scripts
- Executing import.sql

This operation has overhead, so use it judiciously. For tests that don't modify data or use transactions with rollback, the annotation is not necessary.

## Implementation Details

The annotation is implemented using:
- `ResetAfterTestExtension`: JUnit 5 extension that handles the reset logic
- `DatabaseResetService`: Service that performs the actual database operations
- Flyway integration for migration execution
- Hibernate Reactive for SQL script execution

## Configuration

The annotation requires:
- Flyway to be enabled in test profile (automatically configured)
- Access to migration scripts in `src/main/resources/db/migration/`
- Access to import script at `src/main/resources/db/data/import.sql`
