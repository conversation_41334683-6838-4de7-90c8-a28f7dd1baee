package org.galiasystems.csms.test.extensions;

import io.quarkus.arc.Arc;
import io.quarkus.test.vertx.UniAsserter;
import org.galiasystems.csms.test.services.DatabaseResetService;
import org.junit.jupiter.api.extension.AfterEachCallback;
import org.junit.jupiter.api.extension.ExtensionContext;

import java.lang.reflect.Method;
import java.util.logging.Logger;

/**
 * JUnit 5 extension that handles database reset after test execution.
 * This extension is automatically applied when using the @ResetAfterTest annotation.
 */
public class ResetAfterTestExtension implements AfterEachCallback {

    private static final Logger logger = Logger.getLogger(ResetAfterTestExtension.class.getName());

    @Override
    public void afterEach(ExtensionContext context) throws Exception {
        Method testMethod = context.getRequiredTestMethod();

        // Check if the test method has the @ResetAfterTest annotation
        if (testMethod.isAnnotationPresent(org.galiasystems.csms.test.annotations.ResetAfterTest.class)) {
            logger.info("Resetting database after test: " + testMethod.getName());

            try {
                // Check if CDI container is available (i.e., this is a Quarkus test)
                if (Arc.container() == null) {
                    logger.info("CDI container not available - skipping database reset (not a Quarkus test)");
                    return;
                }

                // Get the DatabaseResetService from the CDI container
                DatabaseResetService resetService = Arc.container()
                    .instance(DatabaseResetService.class)
                    .get();

                if (resetService != null) {
                    // Execute the database reset synchronously
                    resetService.resetDatabase()
                        .await()
                        .indefinitely();

                    logger.info("Database reset completed for test: " + testMethod.getName());
                } else {
                    logger.warning("DatabaseResetService not available - skipping database reset");
                }

            } catch (Exception e) {
                logger.severe("Failed to reset database after test " + testMethod.getName() + ": " + e.getMessage());
                // Don't throw the exception to avoid failing the test
                // The test itself should have passed, we just log the reset failure
            }
        }
    }
}
